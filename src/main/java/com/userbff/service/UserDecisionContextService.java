package com.userbff.service;

import com.userbff.entity.UserDecisionContext;
import com.userbff.enums.ScenarioFeedback;
import com.userbff.enums.ScenarioFlag;
import com.userbff.enums.ScenarioType;
import com.userbff.exception.BusinessLogicException;
import com.userbff.exception.ErrorCode;
import com.userbff.mapper.UserDecisionContextMapper;
import com.userbff.model.UserDecisionContextDto;
import com.userbff.model.onboarding.CheckDto;
import com.userbff.model.restrictions.RestrictionsResponseDto;
import com.userbff.model.user.UserInfoDto;
import com.userbff.model.user_session.WarningActionRequestDto;
import com.userbff.repository.UserDecisionContextRepository;
import com.userbff.service.onboarding.OnboardingService;
import com.userbff.service.restrictions.UserRestrictionsService;
import com.userbff.service.user.UserService;
import com.userbff.util.UserDecisionContextUtil;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class UserDecisionContextService {
    private final UserService userService;
    private final UserDecisionContextRepository userDecisionContextRepository;
    private final UserDecisionContextMapper userDecisionContextMapper;
    private final UserRestrictionsService userRestrictionsService;
    private final OnboardingService onboardingService;

    @Transactional
    @Nullable
    public UserDecisionContextDto createOrUpdateUserDecisionContext(
            String userId,
            RestrictionsResponseDto restrictions,
            CheckDto onboardingResponse
    ) {
        UserDecisionContext userDecisionContext = userDecisionContextRepository.findByUserId(userId)
                .orElse(new UserDecisionContext());
        UserDecisionContextDto userDecisionContextDto = userDecisionContextMapper.toDTO(userDecisionContext);
        UserDecisionContextUtil.fillUserDecisionContext(userDecisionContextDto, userId, restrictions, onboardingResponse);
        if (userDecisionContextDto.getScenarioType() == ScenarioType.NONE) {
            userDecisionContextRepository.delete(userDecisionContext);
            return null;
        }
        userDecisionContext = userDecisionContextMapper.toEntity(userDecisionContextDto);
        userDecisionContextRepository.save(userDecisionContext);
        return userDecisionContextDto;
    }

    @Transactional
    public UserDecisionContextDto updateUserDecisionContextOnFeedbackAction(WarningActionRequestDto warningAction) {
        UserDecisionContext userDecisionContext;
        UserInfoDto user = null;
        String userId = null;
        UserDecisionContextDto userDecisionContextDTO = null;
        Long contextId = warningAction.userDecisionContextId();
        if (contextId != null) {
            userDecisionContext = userDecisionContextRepository.findById(contextId)
                    .orElseThrow(() -> new BusinessLogicException(
                            ErrorCode.USER_CONTEXT_NOT_FOUND_BY_ID,
                            ErrorCode.USER_CONTEXT_NOT_FOUND_BY_ID.getDescription().formatted(contextId),
                            ErrorCode.USER_CONTEXT_NOT_FOUND_BY_ID.getDescription().formatted(contextId)
                    ));
        } else {
            user = userService.getUser();
            userId = user.userId().toString();
            Optional<UserDecisionContext> userDecisionContextOpt = userDecisionContextRepository.findByUserId(userId);

            if (!userDecisionContextOpt.isPresent()) {
                userDecisionContextDTO = new UserDecisionContextDto();
                userDecisionContextDTO.setScenarioType(ScenarioType.NONE);
                return userDecisionContextDTO;
            }

            userDecisionContext = userDecisionContextOpt.get();
        }
        userDecisionContextDTO = userDecisionContextMapper.toDTO(userDecisionContext);
        ScenarioFeedback scenarioFeedback = warningAction.scenarioFeedback();

        if (scenarioFeedback == ScenarioFeedback.CLOSED) {
            userDecisionContextDTO.setScenarioFeedback(ScenarioFeedback.CLOSED);
            userDecisionContextDTO.setScenarioFeedbackTimestamp(LocalDate.now());
            userDecisionContextDTO.getScenarioFlags().remove(ScenarioFlag.SHOW_ON_START);
        } else if (scenarioFeedback == ScenarioFeedback.OPENED) {
            userDecisionContextDTO.setScenarioFeedback(ScenarioFeedback.OPENED);
            userDecisionContextDTO.setScenarioFeedbackTimestamp(LocalDate.now());
            userDecisionContextDTO.getScenarioFlags().remove(ScenarioFlag.SHOW_ON_START);
        } else if (scenarioFeedback == ScenarioFeedback.COMPLETED) {
            if (userId == null) {
                user = userService.getUser();
                userId = user.userId().toString();
            }
            RestrictionsResponseDto restrictions = userRestrictionsService.getUserRestrictions(userId);
            if (restrictions.customerDataProcessing() == null) {
                if (userDecisionContextDTO.getScenarioType() == ScenarioType.ADDRESS_UPDATE) {
                    userDecisionContextDTO.setScenarioType(ScenarioType.ADDRESS_IS_UNDER_REVIEW);
                } else {
                    userDecisionContextDTO.setScenarioType(ScenarioType.DATA_IS_UNDER_REVIEW);
                }
            } else {
                CheckDto onboardingResponse = onboardingService.getOnboardingCheck();
                UserDecisionContextUtil.handleCustomerDataProcessing(userDecisionContextDTO, restrictions, onboardingResponse);
            }
        } else {
            throw new BusinessLogicException(
                    ErrorCode.NOT_IMPLEMENTED_YET,
                    ErrorCode.NOT_IMPLEMENTED_YET.getDescription(),
                    ErrorCode.NOT_IMPLEMENTED_YET.getDescription()
            );
        }

        if (userDecisionContextDTO.getScenarioType() == ScenarioType.NONE) {
            userDecisionContextRepository.delete(userDecisionContext);
            return userDecisionContextDTO;
        }

        userDecisionContext = userDecisionContextMapper.toEntity(userDecisionContextDTO);
        userDecisionContextRepository.save(userDecisionContext);
        return userDecisionContextDTO;
    }
}
