package com.userbff.service.identification_external;

import com.userbff.client.ApelsinClient;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.user_session.CodeDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import static com.userbff.enums.HttpMethodType.POST;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.APELSIN_SERVICE;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;

@Slf4j
@Service
public class IdentificationByExternalService {
    private static final String IDENTIFICATION_BY_EXTERNAL_URL = "/identification-external";
    private final ApelsinClient apelsinClient;
    private final MeterRegistry meterRegistry;
    private final Counter identificationByExternalRequestCounter;
    private final Counter identificationByExternalSuccessCounter;
    private final Counter identificationByExternalFailureCounter;
    private final Timer identificationByExternalTimer;

    public IdentificationByExternalService(
            ApelsinClient apelsinClient,
            MeterRegistry meterRegistry
    ) {
        this.apelsinClient = apelsinClient;
        this.meterRegistry = meterRegistry;

        identificationByExternalRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, IDENTIFICATION_BY_EXTERNAL_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        identificationByExternalSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, IDENTIFICATION_BY_EXTERNAL_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        identificationByExternalFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, IDENTIFICATION_BY_EXTERNAL_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        identificationByExternalTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, IDENTIFICATION_BY_EXTERNAL_URL
        );
    }

    public void identificationByExternal(CodeDto codeDto) {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            identificationByExternalRequestCounter.increment();
            log.debug("invoke IdentificationByExternalService.identificationByExternal for: {}", codeDto.toString());

            ResponseEntity<?> response = apelsinClient.identificationByExternal(codeDto);

            identificationByExternalSuccessCounter.increment();
            log.debug("response code IdentificationByExternalService.identificationByExternal: {}", response.getStatusCode());
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException(
                        "Apelsin Service returns status: %d on identification external"
                                .formatted(response.getStatusCodeValue())
                );
            }
        } catch (RetryableException e) {
            identificationByExternalFailureCounter.increment();
            log.error("Apelsin Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException("Apelsin Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            identificationByExternalFailureCounter.increment();
            log.error("An error occurred while identification external: " + e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while identification external: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            identificationByExternalFailureCounter.increment();
            log.error("An error occurred while identification external: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(identificationByExternalTimer);
        }
    }
}
