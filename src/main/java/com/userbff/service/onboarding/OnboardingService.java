package com.userbff.service.onboarding;

import com.userbff.client.ApelsinClient;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.common.ResponseData;
import com.userbff.model.onboarding.CheckDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.userbff.enums.HttpMethodType.GET;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.APELSIN_SERVICE;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;

@Slf4j
@Service
public class OnboardingService {
    private static final String ONBOARDING_CHECK_URL = "/onboarding/check";
    private final ApelsinClient apelsinClient;
    private final MeterRegistry meterRegistry;
    private final Counter onboardingCheckRequestCounter;
    private final Counter onboardingCheckSuccessCounter;
    private final Counter onboardingCheckFailureCounter;
    private final Timer onboardingCheckTimer;

    public OnboardingService(
            ApelsinClient apelsinClient,
            MeterRegistry meterRegistry
    ) {
        this.apelsinClient = apelsinClient;
        this.meterRegistry = meterRegistry;

        onboardingCheckRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, ONBOARDING_CHECK_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        onboardingCheckSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, ONBOARDING_CHECK_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        onboardingCheckFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, ONBOARDING_CHECK_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        onboardingCheckTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, ONBOARDING_CHECK_URL
        );
    }

    public CheckDto getOnboardingCheck() {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            log.info("invoke OnboardingService.getOnboardingCheck()");
            onboardingCheckRequestCounter.increment();

            ResponseEntity<ResponseData<CheckDto>> response = apelsinClient.getOnboardingCheck();

            onboardingCheckSuccessCounter.increment();
            log.debug("response code OnboardingService.getOnboardingCheck: {}", response.getStatusCode());
//            //todo: remove mock
//            CheckDto checkDto = new CheckDto(true, true, IdentificationState.SUCCESS);
//            return checkDto;
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException(
                        "Apelsin Service returns status %d on getting onboarding check"
                                .formatted(response.getStatusCodeValue())
                );
            }

            CheckDto result = Optional.ofNullable(response.getBody())
                    .map(ResponseData::data)
                    .orElseThrow(() -> new ServiceUnavailableException(
                                    "Apelsin Service returns empty body on getting onboarding check"
                            )
                    );
            log.debug("response OnboardingService.getOnboardingCheck: {}", result);
            return result;
        } catch (RetryableException e) {
            onboardingCheckFailureCounter.increment();
            log.error("Apelsin Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException("Apelsin Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            onboardingCheckFailureCounter.increment();
            log.error("An error occurred while getting onboarding check: " + e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while getting onboarding check: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            onboardingCheckFailureCounter.increment();
            log.error("An error occurred while getting onboarding check: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(onboardingCheckTimer);
        }
    }
}
