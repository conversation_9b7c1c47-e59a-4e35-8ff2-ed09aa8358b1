package com.userbff.service.user;

import static com.userbff.enums.HttpMethodType.GET;
import static com.userbff.enums.HttpMethodType.PATCH;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.APELSIN_SERVICE;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.STATUS_TAG;

import com.userbff.client.ApelsinClient;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.common.ResponseData;
import com.userbff.model.user.UserDeviceDto;
import com.userbff.model.user.UserInfoDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import uz.uzum.commons.starter.data.entity.common.enums.Lang;

@Slf4j
@Service
public class UserService {
    private static final String USER_URL = "/user";
    private static final String USER_ACTIVITY_URL = "/user-activity";
    private final ApelsinClient apelsinClient;
    private final MeterRegistry meterRegistry;
    private final Counter userRequestCounter;
    private final Counter userSuccessCounter;
    private final Counter userFailureCounter;
    private final Timer userTimer;
    private final Counter userActivityRequestCounter;
    private final Counter userActivitySuccessCounter;
    private final Counter userActivityFailureCounter;
    private final Timer userActivityTimer;

    public UserService(ApelsinClient apelsinClient, MeterRegistry meterRegistry) {
        this.apelsinClient = apelsinClient;
        this.meterRegistry = meterRegistry;

        userRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                GET.name(),
                HTTP_REQUEST_URL_TAG,
                USER_URL,
                STATUS_TAG,
                REQUEST.getStatus());
        userSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                GET.name(),
                HTTP_REQUEST_URL_TAG,
                USER_URL,
                STATUS_TAG,
                SUCCESS.getStatus());
        userFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                GET.name(),
                HTTP_REQUEST_URL_TAG,
                USER_URL,
                STATUS_TAG,
                FAILURE.getStatus());
        userTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                GET.name(),
                HTTP_REQUEST_URL_TAG,
                USER_URL);

        userActivityRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                PATCH.name(),
                HTTP_REQUEST_URL_TAG,
                USER_ACTIVITY_URL,
                STATUS_TAG,
                REQUEST.getStatus());
        userActivitySuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                PATCH.name(),
                HTTP_REQUEST_URL_TAG,
                USER_ACTIVITY_URL,
                STATUS_TAG,
                SUCCESS.getStatus());
        userActivityFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                PATCH.name(),
                HTTP_REQUEST_URL_TAG,
                USER_ACTIVITY_URL,
                STATUS_TAG,
                FAILURE.getStatus());
        userActivityTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG,
                APELSIN_SERVICE,
                HTTP_REQUEST_TYPE_TAG,
                PATCH.name(),
                HTTP_REQUEST_URL_TAG,
                USER_ACTIVITY_URL);
    }

    public UserInfoDto getUser() {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            userRequestCounter.increment();
            log.info("invoke UserService.getUser()");

            ResponseEntity<ResponseData<UserInfoDto>> response = apelsinClient.getUser();

            userSuccessCounter.increment();
            //            //todo: remove mock
            //            UserInfoDto user = new UserInfoDto("Apelsin", "Apelsin", "customerId",
            //                    "<EMAIL>", "+1239991234567", true, UserRole.USER, UserType.CLIENT,
            //                    UserState.ACTIVE, 3681L, "v1", "url", true, LocalDate.now().toEpochDay(), true,
            //                    true, true, false, "customerId", 3861L, true);
            //            return user;

            log.debug("response code UserService.getUser: {}", response.getStatusCode());
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException(
                        "Apelsin Service returns status: %d on getting user".formatted(response.getStatusCodeValue()));
            }

            UserInfoDto result = Optional.ofNullable(response.getBody())
                    .map(ResponseData::data)
                    .orElseThrow(() ->
                            new ServiceUnavailableException("Apelsin Service returns empty body on getting user"));
            log.debug("response UserService.getUser: {}", result);
            return result;
        } catch (RetryableException e) {
            userFailureCounter.increment();
            log.error("Apelsin Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException(
                    "Apelsin Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            userFailureCounter.increment();
            log.error("An error occurred while receiving user: {}", e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while receiving user: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            userFailureCounter.increment();
            log.error("An error occurred while receiving user: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(userTimer);
        }
    }

    public void updateUserActivity() {
        Timer.Sample sample = Timer.start(meterRegistry);

        try {
            userActivityRequestCounter.increment();
            log.info("Invoke updating user activity");

            ResponseEntity<?> response = apelsinClient.updateUserActivity();

            userActivitySuccessCounter.increment();
            log.debug("response code UserService.updateUserActivity: {}", response.getStatusCode());
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new ServiceUnavailableException("Apelsin Service returns status: %d on updating user activity"
                        .formatted(response.getStatusCodeValue()));
            }
        } catch (RetryableException e) {
            userActivityFailureCounter.increment();
            log.error("Apelsin Service is temporarily unavailable, please try again later", e);
            throw new ServiceUnavailableException(
                    "Apelsin Service is temporarily unavailable, please try again later", e);
        } catch (FeignException e) {
            userActivityFailureCounter.increment();
            log.error("An error occurred while updating user activity: {}", e.contentUTF8(), e);
            throw new FeignClientException("An error occurred while updating user activity: " + e.contentUTF8(), e);
        } catch (Exception e) {
            Sentry.captureException(e);
            userActivityFailureCounter.increment();
            log.error("An error occurred while updating user activity: {}", e.getMessage(), e);
            throw e;
        } finally {
            sample.stop(userActivityTimer);
        }
    }

    public Lang getLang(String userId) {
        return apelsinClient.getUserDevices(userId).stream()
                .map(UserDeviceDto::lang)
                .findAny()
                .orElse(Lang.UZ);
    }
}
