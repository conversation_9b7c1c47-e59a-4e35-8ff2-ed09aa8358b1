package com.userbff.service.kyc;

import com.userbff.client.KYCClient;
import com.userbff.exception.BusinessLogicException;
import com.userbff.exception.ErrorCode;
import com.userbff.exception.FeignClientException;
import com.userbff.exception.ServiceUnavailableException;
import com.userbff.model.kyc.KYCBaseResponseDto;
import com.userbff.model.kyc.KYCUserAddressUpdateRequestDto;
import com.userbff.model.kyc.regions.RegionsDistrictResponseDto;
import feign.FeignException;
import feign.RetryableException;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.sentry.Sentry;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.plexus.util.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.userbff.enums.HttpMethodType.GET;
import static com.userbff.enums.HttpMethodType.POST;
import static com.userbff.enums.RequestStatus.FAILURE;
import static com.userbff.enums.RequestStatus.REQUEST;
import static com.userbff.enums.RequestStatus.SUCCESS;
import static com.userbff.util.MetricsConstants.DESTINATION_TAG;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST;
import static com.userbff.util.MetricsConstants.FEIGN_REQUEST_TIMER;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_TYPE_TAG;
import static com.userbff.util.MetricsConstants.HTTP_REQUEST_URL_TAG;
import static com.userbff.util.MetricsConstants.KYC_SERVICE;
import static com.userbff.util.MetricsConstants.STATUS_TAG;

@Slf4j
@Service
public class KYCService {
    private static final String REGIONS_DISTRICT_DICTIONARY_URL = "/v1/abs/dictionaries/regions-districts";
    private static final String UPDATE_REGISTRATION_ADDRESS_URL = "/v1/customer/registration-address";
//    private final KYCClient kycClient;
    private final MeterRegistry meterRegistry;
    private final Counter regionsDistrictDictionaryRequestCounter;
    private final Counter regionsDistrictDictionarySuccessCounter;
    private final Counter regionsDistrictDictionaryFailureCounter;
    private final Timer regionsDistrictDictionaryTimer;
    private final Counter updateRegistrationAddressRequestCounter;
    private final Counter updateRegistrationAddressSuccessCounter;
    private final Counter updateRegistrationAddressFailureCounter;
    private final Timer updateRegistrationAddressTimer;

    public KYCService(
//            KYCClient kycClient,
            MeterRegistry meterRegistry
    ) {
//        this.kycClient = kycClient;
        this.meterRegistry = meterRegistry;

        regionsDistrictDictionaryRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, REGIONS_DISTRICT_DICTIONARY_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        regionsDistrictDictionarySuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, REGIONS_DISTRICT_DICTIONARY_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        regionsDistrictDictionaryFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, REGIONS_DISTRICT_DICTIONARY_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        regionsDistrictDictionaryTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, GET.name(),
                HTTP_REQUEST_URL_TAG, REGIONS_DISTRICT_DICTIONARY_URL
        );

        updateRegistrationAddressRequestCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, UPDATE_REGISTRATION_ADDRESS_URL,
                STATUS_TAG, REQUEST.getStatus()
        );
        updateRegistrationAddressSuccessCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, UPDATE_REGISTRATION_ADDRESS_URL,
                STATUS_TAG, SUCCESS.getStatus()
        );
        updateRegistrationAddressFailureCounter = meterRegistry.counter(
                FEIGN_REQUEST,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, UPDATE_REGISTRATION_ADDRESS_URL,
                STATUS_TAG, FAILURE.getStatus()
        );
        updateRegistrationAddressTimer = meterRegistry.timer(
                FEIGN_REQUEST_TIMER,
                DESTINATION_TAG, KYC_SERVICE,
                HTTP_REQUEST_TYPE_TAG, POST.name(),
                HTTP_REQUEST_URL_TAG, UPDATE_REGISTRATION_ADDRESS_URL
        );
    }

//    public RegionsDistrictResponseDto getRegionsDistrictDictionary() {
//        Timer.Sample sample = Timer.start(meterRegistry);
//
//        try {
//            regionsDistrictDictionaryRequestCounter.increment();
//            log.info("invoke KYCService.getRegionsDistrictDictionary()");
//
//            ResponseEntity<RegionsDistrictResponseDto> response = kycClient.getRegionsDistrictDictionary();
//
//            String responseCode = Optional.ofNullable(response.getBody())
//                    .map(RegionsDistrictResponseDto::getResponseCode)
//                    .orElse(null);
//            String responseDetails = Optional.ofNullable(response.getBody())
//                    .map(RegionsDistrictResponseDto::getDetails)
//                    .orElse(null);
//
//            regionsDistrictDictionarySuccessCounter.increment();
//            if (!response.getStatusCode().is2xxSuccessful() || StringUtils.equalsIgnoreCase("RC20000", responseCode)) {
//                throw new ServiceUnavailableException(
//                        "KYC Service returns status: %d on getting regions-district dictionary, msg: %s"
//                                .formatted(response.getStatusCodeValue(), responseDetails)
//                );
//            }
//            log.debug("response KYCService.getRegionsDistrictDictionary: {}", response.getBody());
//
//            return response.getBody();
//        } catch (RetryableException e) {
//            regionsDistrictDictionaryFailureCounter.increment();
//            log.error("KYC Service is temporarily unavailable, please try again later", e);
//            throw new ServiceUnavailableException("KYC Service is temporarily unavailable, please try again later", e);
//        } catch (FeignException e) {
//            regionsDistrictDictionaryFailureCounter.increment();
//            log.error("An error occurred while getting regions district dictionary: {}", e.contentUTF8(), e);
//            throw new FeignClientException("An error occurred while getting regions district dictionary: " + e.contentUTF8(), e);
//        } catch (Exception e) {
//            Sentry.captureException(e);
//            regionsDistrictDictionaryFailureCounter.increment();
//            log.error("An error occurred while getting regions district dictionary: {}", e.getMessage(), e);
//            throw e;
//        } finally {
//            sample.stop(regionsDistrictDictionaryTimer);
//        }
//    }

//    public void updateRegistrationAddress(KYCUserAddressUpdateRequestDto request) {
//        Timer.Sample sample = Timer.start(meterRegistry);
//
//        try {
//            updateRegistrationAddressRequestCounter.increment();
//            log.info("invoke KYCService.updateRegistrationAddress()");
//
//            ResponseEntity<KYCBaseResponseDto> response = kycClient.updateRegistrationAddress(request);
//
//            String responseCode = Optional.ofNullable(response.getBody())
//                    .map(KYCBaseResponseDto::getResponseCode)
//                    .orElse(null);
//            String responseDetails = Optional.ofNullable(response.getBody())
//                    .map(KYCBaseResponseDto::getDetails)
//                    .orElse(null);
//
//            updateRegistrationAddressSuccessCounter.increment();
//            log.debug("response code KYCService.updateRegistrationAddress: {}", response.getStatusCode());
//            if (!response.getStatusCode().is2xxSuccessful()) {
//                if (StringUtils.equalsIgnoreCase("RC40002", responseCode)) {
//                    throw new BusinessLogicException(
//                            ErrorCode.KYC_NO_IDENTIFICATION_REQUEST_FOR_ADDRESS_UPDATE,
//                            ErrorCode.KYC_NO_IDENTIFICATION_REQUEST_FOR_ADDRESS_UPDATE.getDescription(),
//                            ErrorCode.KYC_NO_IDENTIFICATION_REQUEST_FOR_ADDRESS_UPDATE.getDescription()
//                    );
//                } else {
//                    throw new ServiceUnavailableException(
//                            "KYC Service returns status: %d on update registration address, msg: %s"
//                                    .formatted(response.getStatusCodeValue(), responseDetails)
//                    );
//                }
//            }
//        } catch (RetryableException e) {
//            updateRegistrationAddressFailureCounter.increment();
//            throw new ServiceUnavailableException("KYC Service is temporarily unavailable, please try again later", e);
//        } catch (FeignException e) {
//            updateRegistrationAddressFailureCounter.increment();
//            throw new FeignClientException("An error occurred while update registration address: " + e.contentUTF8(), e);
//        } catch (Exception e) {
//            Sentry.captureException(e);
//            updateRegistrationAddressFailureCounter.increment();
//            log.error("An error occurred while update registration address: {}", e.getMessage(), e);
//            throw e;
//        } finally {
//            sample.stop(updateRegistrationAddressTimer);
//        }
//    }
}
